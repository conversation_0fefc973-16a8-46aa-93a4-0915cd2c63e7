import { useState, useEffect, useCallback } from "react";
import { Plus, Loader2, AlertCircle, RefreshCw } from "lucide-react";
import type { Series, SeriesFilters, SeriesStatus } from "../types/series";
import {
  useSeries,
  useDeleteSeries,
  useQuickUpdateSeries,
} from "../hooks/useSeries";
import { SeriesCard } from "./SeriesCard";
import { SearchFilter } from "./SearchFilter";
import { Pagination } from "./Pagination";
import { AddSeriesModal } from "./AddSeriesModal";
import { EditSeriesModal } from "./EditSeriesModal";
import { ConfirmDialog } from "./ConfirmDialog";
import { Button } from "./ui/Button";

export function SeriesList() {
  const [filters, setFilters] = useState<SeriesFilters>({
    page: 1,
    pageSize: 20,
    sortBy: "name",
    sortOrder: "asc",
  });

  const [showAddModal, setShowAddModal] = useState(false);
  const [editingSeries, setEditingSeries] = useState<Series | null>(null);
  const [deletingSeries, setDeletingSeries] = useState<Series | null>(null);

  const { data, isLoading, error, refetch, isFetching } = useSeries(filters);
  const deleteSeries = useDeleteSeries();
  const quickUpdateSeries = useQuickUpdateSeries();

  const handleFiltersChange = useCallback((newFilters: SeriesFilters) => {
    setFilters(newFilters);
  }, []);

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handlePageSizeChange = (pageSize: number) => {
    setFilters((prev) => ({ ...prev, pageSize, page: 1 }));
  };

  const handleEdit = (series: Series) => {
    setEditingSeries(series);
  };

  const handleDelete = (series: Series) => {
    setDeletingSeries(series);
  };

  const handleConfirmDelete = async () => {
    if (!deletingSeries) return;

    try {
      await deleteSeries.mutateAsync(deletingSeries.id);
      setDeletingSeries(null);
    } catch (error) {
      console.error("Failed to delete series:", error);
    }
  };

  const handleStatusChange = async (
    series: Series,
    newStatus: SeriesStatus
  ) => {
    try {
      await quickUpdateSeries.mutateAsync({
        id: series.id,
        data: { status: newStatus },
      });
    } catch (error) {
      // Error handling is managed by React Query
    }
  };

  const handleRefresh = () => {
    refetch();
  };

  // Add keyboard shortcut for refresh (Ctrl+R / Cmd+R)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === "r") {
        event.preventDefault();
        handleRefresh();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [refetch]);

  // Only show full loading screen on initial load, not on pagination/filter changes
  if (isLoading && !data) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex items-center gap-2 text-[#3c3c3c]">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span>Loading series...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-[#3c3c3c] mb-2">
            Failed to load series
          </h3>
          <p className="text-gray-600 mb-4">
            {error instanceof Error
              ? error.message
              : "An unexpected error occurred"}
          </p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    );
  }

  // Make sure series is always an array
  const series = Array.isArray(data?.data) ? data.data : [];
  const isEmpty = series.length === 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h1 className="text-5xl sm:text-5xl md:text-6xl lg:text-8xl font-bold text-[#58514b] leading-tight">
            Novel Archives
          </h1>
          <p className="text-lg sm:text-lg md:text-xl lg:text-2xl text-gray-600 mt-1">
            Archive the Adventures You’ve Lived!
          </p>
        </div>
        <div className="flex items-center gap-2 sm:gap-3 flex-shrink-0">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isFetching}
            className="flex items-center gap-2 text-xs sm:text-sm"
          >
            <RefreshCw
              className={`h-3 w-3 sm:h-4 sm:w-4 ${
                isFetching ? "animate-spin" : ""
              }`}
            />
            <span className="hidden sm:inline">Refresh</span>
            <span className="sm:hidden">Refresh</span>
          </Button>
          <Button
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2 text-xs sm:text-sm"
          >
            <Plus className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Add Series</span>
            <span className="sm:hidden">Add</span>
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="relative">
        {isFetching && !isLoading && (
          <div className="absolute top-0 right-0 z-10">
            <div className="flex items-center gap-2 px-3 py-1 bg-blue-50 border border-blue-200 text-sm text-blue-700">
              <Loader2 className="h-3 w-3 animate-spin" />
              Updating...
            </div>
          </div>
        )}
        <SearchFilter filters={filters} onFiltersChange={handleFiltersChange} />
      </div>

      {/* Series Grid */}
      {isEmpty ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg
              className="mx-auto h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No series found
          </h3>
          <p className="text-gray-600 mb-4">
            {filters.search || filters.status
              ? "Try adjusting your search or filters"
              : "Get started by adding your series"}
          </p>
          <Button onClick={() => setShowAddModal(true)}>Add Series</Button>
        </div>
      ) : (
        <>
          <div className="relative">
            {/* Loading overlay for pagination changes */}
            {isFetching && (
              <div className="absolute inset-0 bg-[#f4ecd8] bg-opacity-75 flex items-center justify-center z-10">
                <div className="flex items-center gap-2 px-4 py-2 bg-[#f4ecd8] shadow-sm">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-gray-700">Loading...</span>
                </div>
              </div>
            )}

            <div
              className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 items-start ${
                isFetching ? "opacity-50" : ""
              }`}
            >
              {series.map((seriesItem) => (
                <SeriesCard
                  key={seriesItem.id}
                  series={seriesItem}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onStatusChange={handleStatusChange}
                />
              ))}
            </div>
          </div>

          {/* Pagination */}
          {data && data.totalPages > 1 && (
            <Pagination
              currentPage={filters.page || 1}
              totalPages={data.totalPages}
              pageSize={data.pageSize}
              totalCount={data.totalCount}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
            />
          )}
        </>
      )}

      {/* Modals */}
      <AddSeriesModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
      />

      <EditSeriesModal
        isOpen={!!editingSeries}
        onClose={() => setEditingSeries(null)}
        series={editingSeries}
      />

      <ConfirmDialog
        isOpen={!!deletingSeries}
        onClose={() => setDeletingSeries(null)}
        onConfirm={handleConfirmDelete}
        title="Delete Series"
        message={`Are you sure you want to delete "${deletingSeries?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        loading={deleteSeries.isPending}
      />
    </div>
  );
}
