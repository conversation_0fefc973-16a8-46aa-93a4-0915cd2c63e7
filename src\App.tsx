import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { SeriesList } from "./components/SeriesList";

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false, // Disable to prevent pagination interference
      refetchOnReconnect: true, // Refetch when network reconnects
      staleTime: 5 * 60 * 1000, // 5 minutes - longer stale time for stability
      gcTime: 10 * 60 * 1000, // 10 minutes - longer cache time
      refetchInterval: false, // Disable auto-refresh to prevent pagination interference
      refetchIntervalInBackground: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
          <SeriesList />
        </div>
      </div>
    </QueryClientProvider>
  );
}
