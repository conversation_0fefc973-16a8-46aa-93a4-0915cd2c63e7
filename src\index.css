@import "tailwindcss";

@font-face {
  font-family: "Tagesschrift";
  src: url("/fonts/Tagesschrift.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f4ecd8;
}

::-webkit-scrollbar-thumb {
  background: #3c3c3c;
}

/* Focus styles for better accessibility */
.focus-visible\:ring-2:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

/* Smooth transitions for interactive elements */
* {
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  font-family: "Tagesschrift";
}

body {
  background-color: #f4ecd8;
}

/* Line clamp utilities for consistent text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
