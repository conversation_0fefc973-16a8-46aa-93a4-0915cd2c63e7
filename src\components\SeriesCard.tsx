import { useState } from "react";
import { Edit2, Trash2, Calendar, BookOpen } from "lucide-react";
import type { Series, SeriesStatus } from "../types/series";
import { StatusBadge } from "./StatusBadge";
import { Button } from "./ui/Button";
import { cn } from "../utils/cn";

interface SeriesCardProps {
  series: Series;
  onEdit: (series: Series) => void;
  onDelete: (series: Series) => void;
  onStatusChange?: (series: Series, newStatus: SeriesStatus) => void;
  className?: string;
}

export function SeriesCard({
  series,
  onEdit,
  onDelete,
  onStatusChange,
  className,
}: SeriesCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  // Format date as DD/MM/YYYY
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date
      .toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      })
      .replace(/\//g, "/");
  };

  const formatChapter = (chapter: number) => {
    return chapter % 1 === 0 ? chapter.toString() : chapter.toString();
  };

  const handleStatusClick = () => {
    if (onStatusChange) {
      const statusCycle: SeriesStatus[] = [
        "Reading",
        "Completed",
        "On-Hold",
        "Dropped",
      ];
      const currentIndex = statusCycle.indexOf(series.status as SeriesStatus);
      const nextStatus = statusCycle[(currentIndex + 1) % statusCycle.length];
      onStatusChange(series, nextStatus);
    }
  };

  return (
    <div
      className={cn(
        "bg-[#6b4f3c] p-4 sm:p-6 transition-all duration-200",
        "hover:shadow-md hover:bg-[##3c3c3c]",
        "h-44 sm:h-48 flex flex-col",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Header - title and actions */}
      <div className="flex items-start justify-between mb-2">
        <h3 className="text-lg font-semibold text-[#faf6ee] truncate leading-tight">
          {series.name}
        </h3>
        <div
          className={cn(
            "flex items-center gap-1 transition-opacity duration-200 flex-shrink-0",
            isHovered ? "opacity-100" : "opacity-0"
          )}
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(series)}
            className="p-2 text-[#faf6ee]"
          >
            <Edit2 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(series)}
            className="p-2 text-red-600 hover:text-red-700 hover:bg-[#faf6ee]"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Chapter */}
      <div className="flex items-center gap-1 text-sm mb-1 text-[#faf6ee]">
        <BookOpen className="h-4 w-4" />
        {series.chapter !== null ? (
          <span>Chapter {formatChapter(series.chapter)}</span>
        ) : (
          <span className="opacity-30">No chapter</span>
        )}
      </div>

      {/* Date */}
      <div className="flex items-center gap-1 text-sm mb-1 text-[#faf6ee]">
        <Calendar className="h-4 w-4" />
        <span>{formatDate(series.updatedAt)}</span>
      </div>

      {/* Status Badge */}
      <div className="mt-auto flex items-center">
        <StatusBadge
          status={series.status as SeriesStatus}
          onClick={onStatusChange ? handleStatusClick : undefined}
        />
      </div>
    </div>
  );
}
