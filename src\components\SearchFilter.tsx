import { useState, useEffect, useRef } from "react";
import { Search, Filter, SortAsc, SortDesc } from "lucide-react";
import { Input } from "./ui/Input";
import { Select } from "./ui/Select";
import { Button } from "./ui/Button";
import type { SeriesFilters } from "../types/series";
import { SERIES_STATUSES } from "../types/series";
import { cn } from "../utils/cn";

interface SearchFilterProps {
  filters: SeriesFilters;
  onFiltersChange: (filters: SeriesFilters) => void;
  className?: string;
}

export function SearchFilter({
  filters,
  onFiltersChange,
  className,
}: SearchFilterProps) {
  const [localSearch, setLocalSearch] = useState(filters.search || "");
  const [showFilters, setShowFilters] = useState(false);
  const filtersRef = useRef(filters);

  // Keep filters ref up to date
  useEffect(() => {
    filtersRef.current = filters;
  }, [filters]);

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localSearch !== filters.search) {
        onFiltersChange({
          ...filtersRef.current,
          search: localSearch || undefined,
          page: 1,
        });
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [localSearch, filters.search]); // Only depend on localSearch and filters.search

  const statusOptions = [
    { value: "", label: "All Statuses" },
    ...SERIES_STATUSES.map((status) => ({ value: status, label: status })),
  ];

  const sortOptions = [
    { value: "name", label: "Name" },
    { value: "chapter", label: "Chapter" },
    { value: "status", label: "Status" },
    { value: "updatedAt", label: "Last Updated" },
  ];

  const handleStatusChange = (status: string) => {
    onFiltersChange({
      ...filters,
      status: status || undefined,
      page: 1,
    });
  };

  const handleSortChange = (sortBy: string) => {
    const newFilters = {
      ...filters,
      sortBy: sortBy as SeriesFilters["sortBy"],
      page: 1,
    };

    // When sorting by chapter, exclude series with null chapters
    if (sortBy === "chapter") {
      newFilters.excludeNullChapter = true;
    } else {
      newFilters.excludeNullChapter = undefined;
    }

    onFiltersChange(newFilters);
  };

  const toggleSortOrder = () => {
    const newOrder = filters.sortOrder === "asc" ? "desc" : "asc";
    const newFilters = {
      ...filters,
      sortOrder: newOrder as "asc" | "desc",
    };

    // Maintain excludeNullChapter if we're sorting by chapter
    if (filters.sortBy === "chapter") {
      newFilters.excludeNullChapter = true;
    }

    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    setLocalSearch("");
    onFiltersChange({
      page: 1,
      pageSize: filters.pageSize,
    });
  };

  const hasActiveFilters =
    filters.search ||
    filters.status ||
    filters.sortBy !== "name" ||
    filters.sortOrder !== "asc" ||
    filters.excludeNullChapter;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search and Filter Toggle */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#3c3c3c]" />
          <Input
            type="text"
            placeholder="Search series..."
            value={localSearch}
            onChange={(e) => setLocalSearch(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
          </Button>

          {hasActiveFilters && (
            <Button
              variant="ghost"
              onClick={clearFilters}
              className="text-[#3c3c3c]"
            >
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Advanced Filters */}
      {showFilters && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4 border">
          <Select
            label="Status"
            options={statusOptions}
            value={filters.status || ""}
            onChange={(e) => handleStatusChange(e.target.value)}
          />

          <Select
            label="Sort By"
            options={sortOptions}
            value={filters.sortBy || "name"}
            onChange={(e) => handleSortChange(e.target.value)}
          />

          <div className="space-y-1 sm:col-span-2 lg:col-span-1">
            <label className="block text-sm font-medium text-[#3c3c3c]">
              Sort Order
            </label>
            <Button
              variant="outline"
              onClick={toggleSortOrder}
              className="w-full justify-start"
            >
              {filters.sortOrder === "asc" ? (
                <>
                  <SortAsc className="mr-2 h-4 w-4" />
                  Ascending
                </>
              ) : (
                <>
                  <SortDesc className="mr-2 h-4 w-4" />
                  Descending
                </>
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
