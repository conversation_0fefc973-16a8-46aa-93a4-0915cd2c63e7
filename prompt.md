# Novel Archives Backend Development Guide

## Overview
Create a Hono-based backend API that integrates with the Novel Archives React frontend. The API should support all the series management functionality expected by the frontend.

## Core Requirements

### API Endpoints
Implement these RESTful endpoints:

1. **Series Management**
   - `GET /api/series` - Get paginated series list with filtering/sorting
   - `GET /api/series/:id` - Get series by ID
   - `POST /api/series` - Create new series
   - `PUT /api/series/:id` - Update existing series
   - `DELETE /api/series/:id` - Delete series
   - `GET /api/statuses` - Get available series statuses

### Data Models
Implement the Series model with these fields:
- `id` (string) - Unique identifier
- `name` (string) - Series name
- `chapter` (number | null) - Current chapter (supports decimals)
- `status` (string) - Reading status
- `updatedAt` (string) - ISO date string

### Query Parameters
Support these query parameters for the series list endpoint:
- `search` - Filter by name
- `status` - Filter by status
- `sortBy` - Sort by field (name, chapter, status, updatedAt)
- `sortOrder` - Sort direction (asc, desc)
- `page` - Page number
- `pageSize` - Items per page

### Duplicate Handling
Implement conflict detection when creating series with existing names:
- Return a 409 Conflict status
- Include both the existing series and the proposed data in the response

## Technical Details

### Response Format
For paginated results:
```json
{
  "data": Series[],
  "totalCount": number,
  "page": number,
  "pageSize": number,
  "totalPages": number,
  "hasNextPage": boolean,
  "hasPreviousPage": boolean
}
```

### Status Values
Support these status values:
- "Reading"
- "Completed"
- "On-Hold"
- "Dropped"
- "Cancelled"
- "Plan to Read"

### CORS Configuration
Enable CORS for the frontend domain (http://localhost:5173)

### Error Handling
Return appropriate HTTP status codes and error messages