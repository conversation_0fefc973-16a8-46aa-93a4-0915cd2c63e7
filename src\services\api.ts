import type {
  Series,
  PaginatedResult,
  CreateSeriesRequest,
  UpdateSeriesRequest,
  SeriesFilters,
  DuplicateSeriesResponse,
} from "../types/series";

const API_BASE_URL = "http://localhost:5174/api";

class ApiError extends Error {
  status: number;

  constructor(status: number, message: string) {
    super(message);
    this.status = status;
    this.name = "ApiError";
  }
}

export class DuplicateSeriesError extends Error {
  conflictData: DuplicateSeriesResponse;

  constructor(conflictData: DuplicateSeriesResponse) {
    super("A series with this name already exists");
    this.name = "DuplicateSeriesError";
    this.conflictData = conflictData;
  }
}

async function fetchApi<T>(
  endpoint: string,
  options?: RequestInit
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  try {
    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      // Handle 409 Conflict for duplicate series
      if (response.status === 409) {
        const errorData = await response.json();
        console.log("Received conflict data:", errorData);
        
        // Make sure we have the expected structure
        if (errorData && errorData.existingSeries) {
          throw new DuplicateSeriesError(errorData);
        }
      }

      const errorText = await response.text();
      throw new ApiError(
        response.status,
        errorText || `HTTP ${response.status}`
      );
    }

    // Handle 204 No Content responses
    if (response.status === 204) {
      return null as T;
    }

    return await response.json();
  } catch (error) {
    // Re-throw our custom errors without modification
    if (error instanceof DuplicateSeriesError || error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(
      0,
      `Network error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

export const seriesApi = {
  // Get paginated series list
  getSeries: async (
    filters: SeriesFilters = {}
  ): Promise<PaginatedResult<Series>> => {
    const params = new URLSearchParams();

    if (filters.page) params.append("page", filters.page.toString());
    if (filters.pageSize)
      params.append("pageSize", filters.pageSize.toString());
    if (filters.search) params.append("search", filters.search);
    if (filters.status) params.append("status", filters.status);
    if (filters.sortBy) params.append("sortBy", filters.sortBy);
    if (filters.sortOrder) params.append("sortOrder", filters.sortOrder);
    if (filters.excludeNullChapter) params.append("excludeNullChapter", "true");

    const queryString = params.toString();
    const endpoint = `/series${queryString ? `?${queryString}` : ""}`;

    console.log("Fetching series with URL:", `${API_BASE_URL}${endpoint}`);
    
    try {
      const response = await fetchApi<{success: boolean, data: PaginatedResult<Series>}>(endpoint);
      console.log("API response:", response);
      
      // Extract the actual paginated result from the nested data structure
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new ApiError(500, "Invalid API response format");
    } catch (error) {
      console.error("Error fetching series:", error);
      throw error;
    }
  },

  // Get specific series by ID
  getSeriesById: async (id: string): Promise<Series> => {
    return fetchApi<Series>(`/series/${id}`);
  },

  // Create new series
  createSeries: async (data: CreateSeriesRequest): Promise<Series> => {
    return fetchApi<Series>("/series", {
      method: "POST",
      body: JSON.stringify(data),
    });
  },

  // Update series
  updateSeries: async (
    id: string,
    data: UpdateSeriesRequest
  ): Promise<Series> => {
    return fetchApi<Series>(`/series/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  },

  // Delete series
  deleteSeries: async (id: string): Promise<void> => {
    return fetchApi<void>(`/series/${id}`, {
      method: "DELETE",
    });
  },

  // Get available statuses
  getStatuses: async (): Promise<string[]> => {
    return fetchApi<string[]>("/series/statuses");
  },
};

export { ApiError, DuplicateSeriesError };
