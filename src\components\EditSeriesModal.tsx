import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Modal } from "./ui/Modal";
import { Input } from "./ui/Input";
import { Select } from "./ui/Select";
import { Button } from "./ui/Button";
import type { Series, UpdateSeriesRequest } from "../types/series";
import { SERIES_STATUSES } from "../types/series";
import { useUpdateSeries } from "../hooks/useSeries";

const updateSeriesSchema = z.object({
  name: z
    .string()
    .min(1, "Series name is required")
    .max(200, "Name is too long"),
  chapter: z
    .number()
    .min(0, "Chapter must be 0 or greater")
    .multipleOf(0.1, "Chapter can have at most 1 decimal place")
    .optional()
    .or(z.literal("")),
  status: z.string().min(1, "Status is required"),
});

type UpdateSeriesForm = z.infer<typeof updateSeriesSchema>;

interface EditSeriesModalProps {
  isOpen: boolean;
  onClose: () => void;
  series: Series | null;
}

export function EditSeriesModal({
  isOpen,
  onClose,
  series,
}: EditSeriesModalProps) {
  const updateSeries = useUpdateSeries();

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<UpdateSeriesForm>({
    resolver: zodResolver(updateSeriesSchema),
  });

  const statusOptions = SERIES_STATUSES.map((status) => ({
    value: status,
    label: status,
  }));

  // Reset form when series changes
  useEffect(() => {
    if (series) {
      setValue("name", series.name);
      setValue("chapter", series.chapter ?? "");
      setValue("status", series.status);
    }
  }, [series, setValue]);

  const onSubmit = async (data: UpdateSeriesForm) => {
    if (!series) return;

    try {
      const payload: UpdateSeriesRequest = {
        name: data.name.trim(),
        status: data.status,
      };

      // Only include chapter if it's a valid number
      if (data.chapter !== "" && !isNaN(Number(data.chapter))) {
        payload.chapter = Number(data.chapter);
      } else if (data.chapter === "") {
        payload.chapter = undefined;
      }

      await updateSeries.mutateAsync({ id: series.id, data: payload });

      // Close modal on success
      onClose();
    } catch (error) {
      // Error is handled by React Query and can be displayed via updateSeries.error
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!series) return null;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Edit Series" size="md">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Input
          label="Series Name"
          placeholder="Enter series name..."
          error={errors.name?.message}
          {...register("name")}
        />

        <Input
          label="Current Chapter"
          type="number"
          min="0"
          step="0.1"
          placeholder="0"
          helperText="Supports decimals (e.g., 130.2, 150.7)"
          error={errors.chapter?.message}
          {...register("chapter", {
            setValueAs: (value) => (value === "" ? "" : Number(value)),
          })}
        />

        <Select
          label="Status"
          options={statusOptions}
          error={errors.status?.message}
          {...register("status")}
        />

        {updateSeries.error && (
          <div className="p-3 bg-red-50 border border-red-200">
            <p className="text-sm text-red-600">
              {updateSeries.error instanceof Error
                ? updateSeries.error.message
                : "Failed to update series. Please try again."}
            </p>
          </div>
        )}

        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" loading={isSubmitting} disabled={isSubmitting}>
            Save Changes
          </Button>
        </div>
      </form>
    </Modal>
  );
}
