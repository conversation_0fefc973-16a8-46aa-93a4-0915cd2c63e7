# Novel Archives Frontend

A modern, responsive React frontend for tracking your reading series progress. Built with TypeScript, Tailwind CSS, and React Query.

## Features

### Core Functionality

- **Series Management**: Add, edit, and delete series
- **Progress Tracking**: Track current chapter (supports decimals like 130.2, 150.7) and reading status
- **Duplicate Detection**: Smart conflict resolution when adding series with existing names
- **Search & Filter**: Find series by name, filter by status
- **Sorting**: Sort by name, chapter, status, or last updated
- **Pagination**: Efficient browsing of large series collections

### User Experience

- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile
- **Modern UI**: Clean, intuitive interface with Tailwind CSS
- **Real-time Updates**: Auto-refresh every 60 seconds, refresh on window focus
- **Manual Refresh**: Refresh button and Ctrl+R/Cmd+R keyboard shortcut
- **Loading States**: Smooth loading indicators during API calls
- **Error Handling**: User-friendly error messages and retry options
- **Optimistic Updates**: Instant UI feedback for quick actions

### Status Management

- **Color-coded Badges**: Visual status indicators
- **Quick Status Changes**: Click status badges to cycle through common statuses
- **Available Statuses**: Reading, Completed, On-Hold, Dropped, Cancelled, Plan to Read

## Tech Stack

- **Framework**: React 19 with TypeScript
- **Styling**: Tailwind CSS 4
- **State Management**: TanStack Query (React Query) for server state
- **Form Handling**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Build Tool**: Vite
- **Package Manager**: npm

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Novel Archives Backend API running on `http://localhost:5167`

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd novel-archives-spa
```

2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm start
```

4. Open your browser and navigate to `http://localhost:5173`

### Available Scripts

- `npm start` - Start development server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build

## API Configuration

The frontend is configured to connect to the backend API at `http://localhost:5167`. If your backend is running on a different port, update the `API_BASE_URL` in `src/services/api.ts`.

## Project Structure

```
src/
├── components/
│   ├── ui/                 # Reusable UI components
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   ├── Select.tsx
│   │   └── Modal.tsx
│   ├── SeriesList.tsx      # Main series list component
│   ├── SeriesCard.tsx      # Individual series card
│   ├── AddSeriesModal.tsx  # Add new series modal
│   ├── EditSeriesModal.tsx # Edit series modal
│   ├── StatusBadge.tsx     # Status display component
│   ├── SearchFilter.tsx    # Search and filter controls
│   ├── Pagination.tsx      # Pagination component
│   └── ConfirmDialog.tsx   # Confirmation dialog
├── hooks/
│   └── useSeries.ts        # Custom hooks for series operations
├── services/
│   └── api.ts              # API client and endpoints
├── types/
│   └── series.ts           # TypeScript type definitions
├── utils/
│   └── cn.ts               # Utility functions
├── App.tsx                 # Main application component
├── index.tsx               # Application entry point
└── index.css               # Global styles
```

## Features in Detail

### Series Management

- Add new series with name, current chapter, and status
- Edit existing series information
- Delete series with confirmation dialog
- Bulk operations support (planned)

### Duplicate Conflict Resolution

When adding a series with a name that already exists, the app provides intelligent conflict resolution:

- **Automatic Detection**: Detects duplicate names when creating series
- **Visual Comparison**: Shows side-by-side comparison of existing vs. proposed data
- **Smart Options**:
  - **Update Existing**: Replace existing series data with new information
  - **Change Name**: Modify the name and create as separate series
  - **Cancel**: Abort the operation
- **Seamless UX**: Handles conflicts gracefully without losing user input

### Search and Filtering

- Real-time search by series name
- Filter by reading status
- Sort by multiple criteria (name, chapter, status, date)
- Ascending/descending sort order

### Responsive Design

- Mobile-first approach
- Adaptive grid layout
- Touch-friendly interactions
- Optimized for all screen sizes

### Performance

- Efficient API caching with React Query
- Optimistic updates for better UX
- Debounced search input
- Smooth pagination without page resets
- Background data updates without interruption

## License

This project is licensed under the MIT License.
